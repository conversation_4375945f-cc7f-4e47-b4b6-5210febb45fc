// import { Prediction } from './../app/patients/[id]/types';
// import { createSlice, PayloadAction } from '@reduxjs/toolkit';
// import { XrayImage } from '../app/patients/[id]/types'
 
// interface Annotation {
//     decay: Prediction[];
//     numbering: Prediction[];
//   }
//  interface FullMouthSeriesState {
//     slots: Record<string, XrayImage | null>;
//     analyzing: Record<string, boolean>;
//      uploading: Record<string, boolean>;
//      bulkUploading: boolean;
//     globalAnalyzing: boolean;
//     isCollapsed: boolean;
//     panoramicImage: XrayImage | null;
//     loading: boolean;
//     error: string | null;
//     annotations: Record<string, Annotation>;
// }
 
// const initialState: FullMouthSeriesState = {
//     slots: {},
//     analyzing: {},
//     uploading: {},
//     bulkUploading:false,
//     globalAnalyzing: false,
//     isCollapsed: false,
//     panoramicImage: null,
//     loading: false,
//     error: null,
//     annotations: {},
// };
 
// const fullMouthSeriesSlice = createSlice({
//     name: 'fullMouthSeries',
//     initialState,
//     reducers: {
//         setSlots: (state, action: PayloadAction<Record<string, XrayImage | null>>) => {
//             state.slots = action.payload;
//         },
//         setAnalyzing: (state, action: PayloadAction<Record<string, boolean>>) => {
//             state.analyzing = action.payload;
//         },
//         setUploading: (state, action: PayloadAction<Record<string, boolean>>) => {
//             state.uploading = action.payload;
//         },
//         setIsBulkUploading(state, action: PayloadAction<boolean>) {
//             state.bulkUploading = action.payload;
//           },
//         setGlobalAnalyzing: (state, action: PayloadAction<boolean>) => {
//             state.globalAnalyzing = action.payload;
//         },
//         setIsCollapsed: (state, action: PayloadAction<boolean>) => {
//             state.isCollapsed = action.payload;
//         },
//         toggleIsCollapsed: (state) => {
//             state.isCollapsed = !state.isCollapsed;
//         },
//         setPanoramicImage: (state, action: PayloadAction<XrayImage | null>) => {
//         state.panoramicImage = action.payload;
//     },
//     setLoading: (state, action: PayloadAction<boolean>) => {
//         state.loading = action.payload;
//     },
//     setError: (state, action: PayloadAction<string | null>) => {
//         state.error = action.payload;
//     },
//     setAnnotations: (state, action: PayloadAction<Record<string, Annotation>>) => {
//         state.annotations = action.payload;
//     },
// },
//   });
 
// export const {
//     setSlots,
//     setAnalyzing,
//     setUploading,
//     setGlobalAnalyzing,
//     setIsCollapsed,
//     toggleIsCollapsed,
//     setPanoramicImage,
//     setLoading,
//     setError,
//     setAnnotations,
//     setIsBulkUploading
// } = fullMouthSeriesSlice.actions;
 
// export default fullMouthSeriesSlice.reducer;




import { Prediction } from './../app/patients/[id]/types';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { XrayImage } from '../app/patients/[id]/types'

interface Annotation {
    decay: Prediction[];
    numbering: Prediction[];
}
interface FullMouthSeriesState {
    slots: Record<string, XrayImage | null>;
    analyzing: Record<string, boolean>;
    uploading: Record<string, boolean>;
    bulkUploading: boolean;
    globalAnalyzing: boolean;
    isCollapsed: boolean;
    panoramicImage: XrayImage | null;
    loading: boolean;
    error: string | null;
    annotations: Record<string, Annotation>;
    patientName: string;
    selectedAnnotatorImage: { slotId: string; image: XrayImage; } | null;
    showDecay: boolean;
    showNumbering: boolean;
}

const initialState: FullMouthSeriesState = {
    slots: {},
    analyzing: {},
    uploading: {},
    bulkUploading: false,
    globalAnalyzing: false,
    isCollapsed: false,
    panoramicImage: null,
    loading: false,
    error: null,
    annotations: {},
    patientName: "",
    selectedAnnotatorImage: null,
    showDecay: true,
    showNumbering: false,
};

const fullMouthSeriesSlice = createSlice({
    name: 'fullMouthSeries',
    initialState,
    reducers: {
        setSlots: (state, action: PayloadAction<Record<string, XrayImage | null>>) => {
            state.slots = action.payload;
        },
        setAnalyzing: (state, action: PayloadAction<Record<string, boolean>>) => {
            state.analyzing = action.payload;
        },
        setUploading: (state, action: PayloadAction<Record<string, boolean>>) => {
            state.uploading = action.payload;
        },
        setIsBulkUploading(state, action: PayloadAction<boolean>) {
            state.bulkUploading = action.payload;
        },
        setGlobalAnalyzing: (state, action: PayloadAction<boolean>) => {
            state.globalAnalyzing = action.payload;
        },
        setIsCollapsed: (state, action: PayloadAction<boolean>) => {
            state.isCollapsed = action.payload;
        },
        toggleIsCollapsed: (state) => {
            state.isCollapsed = !state.isCollapsed;
        },
        setPanoramicImage: (state, action: PayloadAction<XrayImage | null>) => {
            state.panoramicImage = action.payload;
        },
        setLoading: (state, action: PayloadAction<boolean>) => {
            state.loading = action.payload;
        },
        setError: (state, action: PayloadAction<string | null>) => {
            state.error = action.payload;
        },
        setAnnotations: (state, action: PayloadAction<Record<string, Annotation>>) => {
            state.annotations = action.payload;
        },
        addPatientName: (state, action: PayloadAction<string>) => {
            state.patientName = action.payload;
        },
        clearPatientName: (state) => {
            state.patientName = "";
        },
        setSelectedAnnotatorImage: (state, action: PayloadAction<{ slotId: string; image: XrayImage; } | null>) => {
            state.selectedAnnotatorImage = action.payload;
        },
        clearSelectedAnnotatorImage: (state) => {
            state.selectedAnnotatorImage = null;
        },
        setShowDecay: (state, action: PayloadAction<boolean>) => {
            state.showDecay = action.payload;
        },
        setShowNumbering: (state, action: PayloadAction<boolean>) => {
            state.showNumbering = action.payload;
        },
        toggleShowDecay: (state) => {
            state.showDecay = !state.showDecay;
        },
        toggleShowNumbering: (state) => {
            state.showNumbering = !state.showNumbering;
        },
    },
});

export const {
    setSlots,
    setAnalyzing,
    setUploading,
    setGlobalAnalyzing,
    setIsCollapsed,
    toggleIsCollapsed,
    setPanoramicImage,
    setLoading,
    setError,
    setAnnotations,
    setIsBulkUploading,
    addPatientName,
    clearPatientName,
    setSelectedAnnotatorImage,
    clearSelectedAnnotatorImage,
    setShowDecay,
    setShowNumbering,
    toggleShowDecay,
    toggleShowNumbering
} = fullMouthSeriesSlice.actions;

export default fullMouthSeriesSlice.reducer;

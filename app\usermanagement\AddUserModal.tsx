import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import fetchWithRefresh from "@/constants/useRefreshAccessToken";
import { useRouter } from "next/navigation";

interface User {
  uid?: string;
  id?: string;
  email: string;
  displayName?: string | null;
  phoneNumber?: string | null;
  firstname?: string;
  middlename?: string;
  lastname?: string;
  clinic_address?: string;
  phone_number?: string;
  role_id?: number;
}

 interface Role {
   id: number;
   rolename: string;
 }

interface AddUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  user?: User | null;
}

interface UserData {
  email: string;
  password: string;
  firstname: string;
  middlename: string;
  lastname: string;
  clinic_address: string;
  phone_number: string;
  role_id: number;
}

interface ApiResponse {
  isError: boolean;
  statusCode: number;
  errorMessage: string;
  data?: any;
}

export default function AddUserModal({
  isOpen,
  onClose,
  onSuccess,
  user,
}: AddUserModalProps) {
  const isEditing = !!user;
  const userId = user?.uid || user?.id;
  const router = useRouter();
  const [userData, setUserData] = useState<UserData>({
    email: "",
    password: "",
    firstname: "",
    middlename: "",
    lastname: "",
    clinic_address: "",
    phone_number: "",
    role_id: 0,
  });
 const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(false);
  const [rolesLoading, setRolesLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});

  useEffect(() => {
    if (user && isOpen) {
      setUserData({
        firstname: user.firstname || "",
        middlename: user.middlename || "",
        lastname: user.lastname || "",
        email: user.email || "",
        password: "", // Don't pre-fill password for editing
        clinic_address: user.clinic_address || "",
        phone_number: user.phone_number || user.phoneNumber || "",
        role_id: user.role_id || 0,
      });
    }
  }, [user, isOpen]);

  // const fetchRoles = async () => {
  //   setRolesLoading(true);
  //   try {
  //     const response = await fetch(
  //       "https://jedaiportal-gpu-714292203960.us-central1.run.app//api/UserManagement/roles"
  //     );
  //     if (!response.ok) {
  //       throw new Error(`HTTP error! status: ${response.status}`);
  //     }

  //     const data: ApiResponse = await response.json();
  //     if (data.isError) {
  //       throw new Error(data.errorMessage || "Failed to fetch roles");
  //     }

  //     setRoles(data.data || []);
  //   } catch (err) {
  //     console.error("Error fetching roles:", err);
  //     setError(err instanceof Error ? err.message : "Failed to fetch roles");
  //   } finally {
  //     setRolesLoading(false);
  //   }
  // };

  const fetchRoles = async () => {
    setRolesLoading(true);
    setError(null);

    try {
      // ← use fetchWithRefresh instead of raw fetch
      const response = await fetchWithRefresh(
        "https://jedaiportal-gpu-714292203960.us-central1.run.app//api/UserManagement/roles",
        { method: "GET", credentials: "include" },
        router
      );

      // ← guard against null (refresh failed → already redirected)
      if (!response) {
        throw new Error("Session expired, please log in again.");
      }
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: ApiResponse = await response.json();
      // console.log("Fetched roles:", data);
      
      if (data.isError) {
        throw new Error(data.errorMessage || "Failed to fetch roles");
      }

setRoles(Array.isArray(data.data) ? data.data : []);
    } catch (err: any) {
      console.error("Error fetching roles:", err);
      setError(err.message || "Failed to fetch roles");
    } finally {
      setRolesLoading(false);
    }
  };
  useEffect(() => {
    if (isOpen) {
      fetchRoles();
    }
  }, [isOpen]);
  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!userData.firstname.trim()) {
      errors.firstname = "First name is required";
    }
    if (!userData.lastname.trim()) {
      errors.lastname = "Last name is required";
    }
    if (!userData.email.trim()) {
      errors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(userData.email)) {
      errors.email = "Email is invalid";
    }
    if (!isEditing && !userData.password.trim()) {
      errors.password = "Password is required";
    } else if (!isEditing && userData.password.length < 6) {
      errors.password = "Password must be at least 6 characters";
    }

    if (userData.role_id === 0) {
      errors.role_id = "Role selection is required";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // const handleSubmit = async () => {
  //   if (!validateForm()) {
  //     return;
  //   }

  //   setLoading(true);
  //   setError(null);
  //   setSuccess(false);

  //   try {
  //     let response;
  //     if (isEditing && userId) {
  //       // Edit user - exclude password from update
  //       const { password, ...updateData } = userData;
  //       response = await fetch(
  //         `https://jedaiportal-gpu-714292203960.us-central1.run.app//api/UserManagement/update/${userId}`,
  //         {
  //           method: "PUT",
  //           headers: {
  //             "Content-Type": "application/json",
  //           },
  //           body: JSON.stringify(updateData),
  //         }
  //       );
  //     } else {
  //       // Add new user
  //       response = await fetch(
  //         "https://jedaiportal-gpu-714292203960.us-central1.run.app//api/UserManagement/create",
  //         {
  //           method: "POST",
  //           headers: {
  //             "Content-Type": "application/json",
  //           },
  //           body: JSON.stringify(userData),
  //         }
  //       );
  //     }

  //     const data: ApiResponse = await response.json();
  //     if (!response.ok || data.isError) {
  //       throw new Error(data.errorMessage || `Failed to ${isEditing ? 'update' : 'add'} user`);
  //     }

  //     setSuccess(true);
  //     resetForm();
  //     setTimeout(() => {
  //       onSuccess();
  //       onClose();
  //     }, 1500);
  //   } catch (err) {
  //     setError(err instanceof Error ? err.message : "An unknown error occurred");
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      const url = isEditing
        ? `https://jedaiportal-gpu-714292203960.us-central1.run.app//api/UserManagement/update/${userId}`
        : `https://jedaiportal-gpu-714292203960.us-central1.run.app//api/UserManagement/create`;

      const method = isEditing ? "PUT" : "POST";
      const bodyData = isEditing
        ? (({ password, ...rest }) => rest)(userData)
        : userData;

      // ← use fetchWithRefresh
      const response = await fetchWithRefresh(
        url,
        {
          method,
          credentials: "include",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(bodyData),
        },
        router
      );

      // ← guard against null (refresh failed → redirected)
      if (!response) {
        throw new Error("Session expired, please log in again.");
      }
      const data: ApiResponse = await response.json();
      if (!response.ok || data.isError) {
        throw new Error(
          data.errorMessage || `Failed to ${isEditing ? "update" : "add"} user`
        );
      }

      setSuccess(true);
      // toast({
      //   title: `User ${isEditing ? "updated" : "created"} successfully`,
      // });
      resetForm();
      setTimeout(() => {
        onSuccess();
        onClose();
      }, 1500);
    } catch (err: any) {
      setError(err.message || "An unknown error occurred");
      // toast({
      //   title: "Error",
      //   description: err.message || "An unexpected error occurred",
      //   variant: "destructive",
      // });
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setUserData({
      email: "",
      password: "",
      firstname: "",
      middlename: "",
      lastname: "",

      clinic_address: "",
      phone_number: "",
      role_id: 0,
    });
    setValidationErrors({});
    setError(null);
    setSuccess(false);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <CardTitle className="text-xl">
            {isEditing ? "Edit User" : "Add New User"}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md border border-red-200">
              {error}
            </div>
          )}

          {success && (
            <div className="mb-4 p-3 bg-green-100 text-green-700 rounded-md border border-green-200">
              User {isEditing ? "updated" : "added"} successfully!
            </div>
          )}

          <div className="space-y-4">
            {/* First Row: Names */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  First Name *
                </label>
                <Input
                  type="text"
                  value={userData.firstname}
                  onChange={(e) => {
                    setUserData({ ...userData, firstname: e.target.value });
                    if (validationErrors.firstname) {
                      setValidationErrors({
                        ...validationErrors,
                        firstname: "",
                      });
                    }
                  }}
                  placeholder="Enter first name"
                  className={validationErrors.firstname ? "border-red-500" : ""}
                />
                {validationErrors.firstname && (
                  <p className="text-red-500 text-xs mt-1">
                    {validationErrors.firstname}
                  </p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Middle Name
                </label>
                <Input
                  type="text"
                  value={userData.middlename}
                  onChange={(e) =>
                    setUserData({ ...userData, middlename: e.target.value })
                  }
                  placeholder="Enter middle name"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Last Name *
                </label>
                <Input
                  type="text"
                  value={userData.lastname}
                  onChange={(e) => {
                    setUserData({ ...userData, lastname: e.target.value });
                    if (validationErrors.lastname) {
                      setValidationErrors({
                        ...validationErrors,
                        lastname: "",
                      });
                    }
                  }}
                  placeholder="Enter last name"
                  className={validationErrors.lastname ? "border-red-500" : ""}
                />
                {validationErrors.lastname && (
                  <p className="text-red-500 text-xs mt-1">
                    {validationErrors.lastname}
                  </p>
                )}
              </div>
            </div>

            {/* Second Row: Email and Password */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email *
                </label>
                <Input
                  type="email"
                  value={userData.email}
                  onChange={(e) => {
                    setUserData({ ...userData, email: e.target.value });
                    if (validationErrors.email) {
                      setValidationErrors({ ...validationErrors, email: "" });
                    }
                  }}
                  placeholder="<EMAIL>"
                  className={validationErrors.email ? "border-red-500" : ""}
                />
                {validationErrors.email && (
                  <p className="text-red-500 text-xs mt-1">
                    {validationErrors.email}
                  </p>
                )}
              </div>
              {!isEditing && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Password *
                  </label>
                  <Input
                    type="password"
                    value={userData.password}
                    onChange={(e) => {
                      setUserData({ ...userData, password: e.target.value });
                      if (validationErrors.password) {
                        setValidationErrors({
                          ...validationErrors,
                          password: "",
                        });
                      }
                    }}
                    placeholder="Enter password"
                    className={
                      validationErrors.password ? "border-red-500" : ""
                    }
                  />
                  {validationErrors.password && (
                    <p className="text-red-500 text-xs mt-1">
                      {validationErrors.password}
                    </p>
                  )}
                </div>
              )}
            </div>

            {/* Third Row: Clinic Name and Role */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Role *
                </label>
               <Select
  value={userData.role_id.toString()}
  onValueChange={val => setUserData({ ...userData, role_id: Number(val) })}
>
  <SelectTrigger>
    <SelectValue placeholder={rolesLoading ? "Loading…" : "Select a role"} />
  </SelectTrigger>
 <SelectContent>
  {roles.length === 0 && !rolesLoading && (
    <SelectItem value="0" disabled>
      No roles found
    </SelectItem>
  )}
  {roles.map(role => (
   <SelectItem key={role.id} value={String(role.id)}>
     {role.rolename}
   </SelectItem>
 ))}
</SelectContent>

</Select>

                {validationErrors.role_id && (
                  <p className="text-red-500 text-xs mt-1">
                    {validationErrors.role_id}
                  </p>
                )}
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-6">
            <Button variant="outline" onClick={handleClose} disabled={loading}>
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={loading || rolesLoading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {loading
                ? isEditing
                  ? "Updating..."
                  : "Adding..."
                : isEditing
                ? "Update User"
                : "Add User"}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// hooks/useIdleLogout.ts
"use client";

import { useEffect, useRef, useCallback, useState } from "react";
import { useRouter } from "next/navigation";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { logout as logoutUtil } from "@/utils/auth";
import { getSessionConfig, formatTimeRemaining } from "@/constants/sessionConfig";


interface UseIdleLogoutReturn {
  isSessionExpired: boolean;
  logout: () => void;
  closeDialog: () => void;
}

export function useIdleLogout(): UseIdleLogoutReturn {
  const router = useRouter();
  const dispatch = useAppDispatch();

  // Get user authentication state
  const accessToken = useAppSelector((state) => state.user.accessToken);
  const isAuthenticated = !!accessToken;

  // Session configuration - reactive to localStorage changes
  const [config, setConfig] = useState(getSessionConfig());

  // Timers and state
  const idleTimer = useRef<ReturnType<typeof setTimeout> | null>(null);
  const logoutTimer = useRef<ReturnType<typeof setTimeout> | null>(null);
  const [isSessionExpired, setIsSessionExpired] = useState(false);

  // // console.log("[useIdleLogout] Session timeout enabled:", config.enabled, "Timeout:", config.timeoutDuration / 60000, "minutes");

  // Listen for localStorage changes to update configuration
  useEffect(() => {
    const handleStorageChange = () => {
      const newConfig = getSessionConfig();
      // // console.log("[useIdleLogout] Configuration updated:", newConfig);
      setConfig(newConfig);
    };

    // Listen for storage events (from other tabs)
    window.addEventListener('storage', handleStorageChange);

    // Also check for changes periodically (for same-tab changes)
    const interval = setInterval(handleStorageChange, 1000);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      clearInterval(interval);
    };
  }, []);



  // Hide dialog when user is no longer authenticated or timeout is disabled
  useEffect(() => {
    if ((!isAuthenticated || !config.enabled) && isSessionExpired) {
      // console.log("[useIdleLogout] User not authenticated or timeout disabled, hiding dialog");
      setIsSessionExpired(false);
    }
  }, [isAuthenticated, config.enabled, isSessionExpired]);

  // Initial cleanup - ensure dialog is hidden on mount if conditions aren't met
  useEffect(() => {
    if (!isAuthenticated || !config.enabled) {
      // console.log("[useIdleLogout] Initial cleanup - hiding dialog");
      setIsSessionExpired(false);
    }
  }, [isAuthenticated, config.enabled]);

  // Show warning dialog before logout
  const showWarningDialog = useCallback(() => {
    // Only show dialog if user is authenticated and session timeout is enabled
    if (!isAuthenticated || !config.enabled) {
      // console.log("[useIdleLogout] Not showing dialog - user not authenticated or timeout disabled");
      return;
    }

    // console.log("[useIdleLogout] Session about to expire, showing warning dialog");

    // Show session expired dialog
    setIsSessionExpired(true);

    // Clear the idle timer
    if (idleTimer.current) clearTimeout(idleTimer.current);

    // Set a timer for automatic logout (using configured warning duration)
    logoutTimer.current = setTimeout(async () => {
      // console.log("[useIdleLogout] Starting logout process...");

      // Hide dialog immediately when logout starts
      setIsSessionExpired(false);

      try {
        await logoutUtil(dispatch, router);
        // console.log("[useIdleLogout] Logout completed successfully");
      } catch (error) {
        console.error("[useIdleLogout] Error during logout:", error);

        // Hide dialog even if logout fails
        setIsSessionExpired(false);

        // Fallback logout - force redirect even if logout fails
        try {
          // Clear storage manually as fallback
          localStorage.clear();
          sessionStorage.clear();

          // Clear cookies manually
          document.cookie.split(";").forEach((c) => {
            const eqPos = c.indexOf("=");
            const name = eqPos > -1 ? c.substring(0, eqPos) : c;
            document.cookie = `${name.trim()}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
          });

          // Force redirect
          window.location.href = "/login";
        } catch (fallbackError) {
          console.error("[useIdleLogout] Fallback logout failed:", fallbackError);
          // Ensure dialog is hidden even in worst case
          setIsSessionExpired(false);
          window.location.href = "/login";
        }
      }
    }, config.warningDuration); // Use configured warning duration
  }, [dispatch, router, config.warningDuration]);



  // Reset the idle timer
  const resetIdleTimer = useCallback(() => {
    // Only reset if user is authenticated and session timeout is enabled
    if (!isAuthenticated || !config.enabled) {
      return;
    }

    // Clear existing timers
    if (idleTimer.current) clearTimeout(idleTimer.current);
    if (logoutTimer.current) clearTimeout(logoutTimer.current);

    // Reset session expired state
    setIsSessionExpired(false);

    // Calculate when to show warning dialog (warningDuration before timeout)
    const warningTime = config.timeoutDuration - config.warningDuration;

    // Ensure warning time is positive (minimum warning duration)
    const actualWarningTime = Math.max(warningTime, 0);

    // Set timer to show warning dialog
    idleTimer.current = setTimeout(() => {
      showWarningDialog();
    }, actualWarningTime);

    // // console.log("[useIdleLogout] Timer reset. Warning in:", actualWarningTime / 60000, "minutes, Total timeout:", config.timeoutDuration / 60000, "minutes");
  }, [isAuthenticated, config.enabled, config.timeoutDuration, showWarningDialog]);

  // Close dialog function (but logout will still happen)
  const closeDialog = useCallback(() => {
    // // console.log("[useIdleLogout] Dialog closed by user, but logout will continue");
    setIsSessionExpired(false);
    // Note: logout timer continues running even if dialog is closed
  }, []);

  // Manual logout function
  const manualLogout = useCallback(async () => {
    // // console.log("[useIdleLogout] Manual logout requested");

    // Clear any pending logout timer
    if (logoutTimer.current) clearTimeout(logoutTimer.current);

    // Hide dialog immediately
    setIsSessionExpired(false);

    try {
      await logoutUtil(dispatch, router);
    } catch (error) {
      console.error("[useIdleLogout] Manual logout error:", error);
      // Ensure dialog is hidden even if logout fails
      setIsSessionExpired(false);
      window.location.href = "/login";
    }
  }, [dispatch, router]);

  // Set up activity listeners
  useEffect(() => {
    if (!isAuthenticated || !config.enabled) {
      // // console.log("[useIdleLogout] Session timeout disabled or user not authenticated");
      return;
    }

    // // console.log("[useIdleLogout] Setting up activity listeners");

    // Add event listeners for user activity
    const handleActivity = () => {
      // Reset timer on any activity (unless session is already expired)
      if (!isSessionExpired) {
        resetIdleTimer();
      }
    };

    config.activityEvents.forEach((event) => {
      document.addEventListener(event, handleActivity, true);
    });

    // Initialize timer
    resetIdleTimer();

    // Cleanup
    return () => {
      // // console.log("[useIdleLogout] Cleaning up activity listeners");

      config.activityEvents.forEach((event) => {
        document.removeEventListener(event, handleActivity, true);
      });

      if (idleTimer.current) clearTimeout(idleTimer.current);
      if (logoutTimer.current) clearTimeout(logoutTimer.current);

      // Ensure dialog is hidden on cleanup
      setIsSessionExpired(false);
    };
  }, [isAuthenticated, config.enabled, config.activityEvents, config.timeoutDuration, isSessionExpired, resetIdleTimer]);

  // Cleanup effect on unmount
  useEffect(() => {
    return () => {
      // // console.log("[useIdleLogout] Component unmounting, cleaning up all timers and state");
      if (idleTimer.current) clearTimeout(idleTimer.current);
      if (logoutTimer.current) clearTimeout(logoutTimer.current);
      setIsSessionExpired(false);
    };
  }, []);

  return {
    isSessionExpired,
    logout: manualLogout,
    closeDialog
  };
}

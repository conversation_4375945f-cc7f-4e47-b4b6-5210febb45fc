// components/IdleHandler.tsx
"use client";

import { ReactNode } from "react";
import { useIdleLogout } from "@/hooks/useIdleLogout";
import { SessionExpiredDialog } from "@/components/SessionWarningDialog";
import { getSessionConfig } from "@/constants/sessionConfig";

export function IdleHandler({ children }: { children: ReactNode }) {
  const { isSessionExpired, logout, closeDialog } = useIdleLogout();
  const config = getSessionConfig();

  return (
    <>
      {children}
      <SessionExpiredDialog
        isOpen={isSessionExpired}
        onAutoLogout={logout}
        onClose={closeDialog}
        warningDurationMs={config.warningDuration}
      />
    </>
  );
}

import { Visit } from '@/app/patients/[id]/types';
import { API_BASE_URL } from '@/constants/apiRoutes';
import fetchWithRefresh from '@/constants/useRefreshAccessToken';
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';


export interface VisitState {
  visits: Visit[]
  loading: boolean
  error: string | null
}

const initialState: VisitState = {
  visits: [],
  loading: false,
  error: null
};

// export const fetchVisits = createAsyncThunk(
//   'visits/fetchVisits',
//   async (patientId: string) => {
//     const response = await fetch(`${API_BASE_URL}/api/Treatment/getAllPatientVisits/${patientId}`);
//     if (!response.ok) {
//       throw new Error('Failed to fetch visits');
//     }
//     const data = await response.json();
//     // console.log("Data:", data);
    
//   return data.map((visit: any) => ({
//   id: visit.visitId.toString(),          // Keep consistent naming
//   visitId: visit.visitId.toString(),     // Add this if needed by components
//   date: new Date(visit.visitDate).toLocaleDateString(),
//   visitDate: new Date(visit.visitDate).toLocaleDateString(), // Add both
//   dentist: visit.dentistName,
//   dentistName: visit.dentistName,        // Add both
//   timestamp: new Date(visit.createdAt).toLocaleString(),
//   xrayCount: visit.imageCount || 0,
//   imageCount: visit.imageCount || 0,     // Add both
//   procedures: visit.procedures || [],
//   xrays: [],
  
// }));
//   }
// );

export const fetchVisits = createAsyncThunk(
  'visits/fetchVisits',
  async (patientId: string) => {
    // “Fake” router for thunk context:
    const router = {
      push: (path: string) => {
        window.location.href = path;
      }
    };

    // 1️⃣ Use fetchWithRefresh instead of fetch
    const response = await fetchWithRefresh(
      `${API_BASE_URL}/api/Treatment/getAllPatientVisits/${patientId}`,
      { method: 'GET', credentials: 'include' },
      // @ts-ignore – our helper expects a router-like object
      router
    );

    // 2️⃣ Handle a null response (refresh failed → already redirected)
    if (!response) {
      throw new Error('Session expired, please log in again.');
    }
    if (!response.ok) {
      throw new Error('Failed to fetch visits');
    }

    // 3️⃣ Parse and map
    const data = await response.json();
    return (data.data ?? data).map((visit: any) => ({
      id: visit.visitId.toString(),
      visitId: visit.visitId.toString(),
      date: new Date(visit.visitDate).toLocaleDateString(),
      visitDate: new Date(visit.visitDate).toLocaleDateString(),
      dentist: visit.dentistName,
      dentistName: visit.dentistName,
      timestamp: new Date(visit.createdAt).toLocaleString(),
      xrayCount: visit.imageCount || 0,
      imageCount: visit.imageCount || 0,
      procedures: visit.procedures || [],
      xrays: [],
    }));
  }
);

// export const createVisit = createAsyncThunk(
//   'visits/createVisit',
//   async ({ patientId, dentistName }: { patientId: string; dentistName: string }) => {
//     const response = await fetch(`${API_BASE_URL}/api/Treatment/createPatientVisit`, {
//       method: 'POST',
//       headers: {
//         'Content-Type': 'application/json',
//       },
//       body: JSON.stringify({
//         patientId: parseInt(patientId),
//         dentistName,
//         updatedBy: "system"
//       })
//     });
//     if (!response.ok) {
//       throw new Error('Failed to create visit');
//     }
//     return await response.json();
//   }
// );

export const createVisit = createAsyncThunk(
  "visits/createVisit",
  async (
    { patientId, dentistName }: { patientId: string; dentistName: string },
    thunkAPI
  ) => {
    // Minimal router-like fallback for thunks
    const router = {
      push: (path: string) => {
        window.location.href = path;
      },
    };

    const response = await fetchWithRefresh(
      `${API_BASE_URL}/api/Treatment/createPatientVisit`,
      {
        method: "POST",
        credentials: "include",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          patientId: parseInt(patientId, 10),
          dentistName,
          updatedBy: "system",
        }),
      },
      // @ts-ignore – fetchWithRefresh expects a router with .push()
      router
    );

    if (!response) {
      // Token refresh failed → already redirected
      return thunkAPI.rejectWithValue("Session expired, please log in again.");
    }
    if (!response.ok) {
      throw new Error("Failed to create visit");
    }

    return await response.json();
  }
);

const visitsSlice = createSlice({
  name: 'visits',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchVisits.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchVisits.fulfilled, (state, action) => {
        state.visits = action.payload;
        state.loading = false;
      })
      .addCase(fetchVisits.rejected, (state, action) => {
        state.error = action.error.message || 'Failed to fetch visits';
        state.loading = false;
      })
      .addCase(createVisit.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createVisit.fulfilled, (state, action) => {
        const newVisit: Visit = {
          id: action.payload.visitId.toString(),
          date: new Date().toLocaleDateString(),
          dentist: action.payload.dentistName,
          timestamp: new Date().toLocaleString(),
          xrayCount: 0,
          procedures: [],
          xrays: []
        };
        state.visits.unshift(newVisit);
        state.loading = false;
      })
      .addCase(createVisit.rejected, (state, action) => {
        state.error = action.error.message || 'Failed to create visit';
        state.loading = false;
      });
  }
});

export default visitsSlice.reducer;
"use client";
 
import type React from "react";
 
import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AlertCircle, Check } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { PATIENTS } from "@/constants/apiRoutes";
import fetchWithRefresh from "@/constants/useRefreshAccessToken";
import { useAppSelector } from "@/store/hooks";
import { RootState } from "@/store/store";
 
interface FormField {
  value: string;
  error: string | null;
  touched: boolean;
  required: boolean;
}
 
interface FormState {
  firstName: FormField;
  middleName: FormField;
  lastName: FormField;
  crmIdentifier: FormField;
}
 
export default function PatientAdditionForm() {
  const router = useRouter();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
const userId = useAppSelector((s: RootState) => s.user.userId);
 
 
  const [formState, setFormState] = useState<FormState>({
    firstName: { value: "", error: null, touched: false, required: true },
    middleName: { value: "", error: null, touched: false, required: false },
    lastName: { value: "", error: null, touched: false, required: true },
    crmIdentifier: { value: "", error: null, touched: false, required: false },
  });
 
  // Regex patterns for validation
  const nameRegex = /^[a-zA-Z][a-zA-Z\s]*$/; // Letters only, no spaces/special chars at start
  const crmRegex = /^[a-zA-Z0-9][a-zA-Z0-9]*$/; // Alphanumeric only, no spaces/special chars at start
 
  const validateFieldInput = (field: keyof FormState, value: string): string | null => {
    // Check if required field is empty
    if (!value && formState[field].required) {
      return "This field is required";
    }
 
    // If field is empty and not required, it's valid
    if (!value && !formState[field].required) {
      return null;
    }
 
    // If field has value, validate the format regardless of whether it's required
    if (value) {
      switch (field) {
        case 'firstName':
        case 'lastName':
        case 'middleName':
          if (!nameRegex.test(value)) {
            return "Only letters and spaces are allowed. Cannot start with space or special characters.";
          }
          break;
        case 'crmIdentifier':
          if (!crmRegex.test(value)) {
            return "Only letters and numbers are allowed. Cannot contain spaces or start with special characters.";
          }
          break;
      }
    }
 
    return null;
  };
 
  const handleInputChange = (field: keyof FormState, value: string) => {
    const error = validateFieldInput(field, value);
 
    setFormState((prev) => ({
      ...prev,
      [field]: {
        ...prev[field],
        value,
        touched: true,
        error,
      },
    }));
  };
 
  const validateForm = () => {
    let isValid = true;
    const newFormState = { ...formState };
 
    // Validate all fields using the same validation logic
    Object.keys(formState).forEach((key) => {
      const field = key as keyof FormState;
      const error = validateFieldInput(field, formState[field].value);
 
      if (error) {
        newFormState[field] = {
          ...newFormState[field],
          error,
          touched: true,
        };
        isValid = false;
      }
    });
 
    setFormState(newFormState);
    return isValid;
  };
 
 
 
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    // console.log("Form State at submit:", formState);
    if (!validateForm()) {
      // console.log("Validation failed:", formState);
      return toast({
        variant: "destructive",
        title: "Please correct the errors",
        description: "Fill in all required fields before submitting.",
      });
    }
 
    setIsSubmitting(true);
 
    try {
      // const userId = Cookies.get("userId") || "";
      const body = {
      firstname: formState.firstName.value,
      middlename: formState.middleName.value,
      lastname: formState.lastName.value,
      crmId: formState.crmIdentifier.value,
      gender: "",
      dob: null,
      phoneNumber: "",
      email: "",
      address1: "",
      address2: "",
      city: "",
      state: "",
      zipCode: "",
      country: "",
      createdBy: userId,
      updatedBy: userId,
    };
 
      // console.log("🚀 Payload to send:", body);
      const res = await fetchWithRefresh(PATIENTS, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(body),
        credentials: "include"
      }, router);
 
   
 
      if (!res || !res.ok) {
        const errorData = await res?.json();
        throw new Error(errorData?.message || "Failed to register patient.");
      }
      const data = await res.json();
      // console.log("📥 Response JSON:", data);
 
      // console.log("✅ Patient registered successfully");
      setIsComplete(true);
      toast({
        title: "Patient added successfully",
        description: "The new patient has been registered.",
      });
 
      // Redirect after a short pause
      setTimeout(() => router.push("/patients"), 1500);
    } catch (error: any) {
      console.error("🛑Registration error:", error);
      toast({
        variant: "destructive",
        title: "Registration failed",
        description: error.message || "An unexpected error occurred.",
      });
      // Redirect to patients page even on error after a short delay
      setTimeout(() => router.push("/patients"), 2000);
    } finally {
      setIsSubmitting(false);
      // console.log("🏁 Submission flow ended");
    }
  };
 
  return (
    <div className="container mx-auto py-6 px-4 max-w-3xl">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-slate-800">
          New Patient Registration
        </h1>
        <p className="text-slate-500">
          Please enter the required patient information
        </p>
      </div>
 
      <Card className="shadow-sm">
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="first-name" className="flex items-center gap-1">
                First Name <span className="text-red-500">*</span>
              </Label>
              <Input
                id="first-name"
                placeholder="Enter first name"
                value={formState.firstName.value}
                onChange={(e) => handleInputChange("firstName", e.target.value)}
                className={cn(
                  "transition-all duration-200 focus:ring-2 focus:ring-blue-500 focus:ring-offset-1",
                  formState.firstName.error &&
                    "border-red-500 focus:ring-red-500"
                )}
              />
              {formState.firstName.error && (
                <p className="text-xs text-red-500 flex items-center gap-1 mt-1">
                  <AlertCircle size={12} />
                  {formState.firstName.error}
                </p>
              )}
            </div>
 
            <div className="space-y-2">
              <Label htmlFor="middle-name">Middle Name</Label>
              <Input
                id="middle-name"
                placeholder="Enter middle name (optional)"
                value={formState.middleName.value}
                onChange={(e) =>
                  handleInputChange("middleName", e.target.value)
                }
                className={cn(
                  "transition-all duration-200 focus:ring-2 focus:ring-blue-500 focus:ring-offset-1",
                  formState.middleName.error &&
                    "border-red-500 focus:ring-red-500"
                )}
              />
              {formState.middleName.error && (
                <p className="text-xs text-red-500 flex items-center gap-1 mt-1">
                  <AlertCircle size={12} />
                  {formState.middleName.error}
                </p>
              )}
            </div>
 
            <div className="space-y-2">
              <Label htmlFor="last-name" className="flex items-center gap-1">
                Last Name <span className="text-red-500">*</span>
              </Label>
              <Input
                id="last-name"
                placeholder="Enter last name"
                value={formState.lastName.value}
                onChange={(e) => handleInputChange("lastName", e.target.value)}
                className={cn(
                  "transition-all duration-200 focus:ring-2 focus:ring-blue-500 focus:ring-offset-1",
                  formState.lastName.error &&
                    "border-red-500 focus:ring-red-500"
                )}
              />
              {formState.lastName.error && (
                <p className="text-xs text-red-500 flex items-center gap-1 mt-1">
                  <AlertCircle size={12} />
                  {formState.lastName.error}
                </p>
              )}
            </div>
 
            <div className="space-y-2">
              <Label
                htmlFor="crm-identifier"
                className="flex items-center gap-1"
              >
                CRM Identifier
              </Label>
              <Input
                id="crm-identifier"
                placeholder="Enter CRM identifier (optional)"
                value={formState.crmIdentifier.value}
                onChange={(e) =>
                  handleInputChange("crmIdentifier", e.target.value)
                }
                className={cn(
                  "transition-all duration-200 focus:ring-2 focus:ring-blue-500 focus:ring-offset-1",
                  formState.crmIdentifier.error &&
                    "border-red-500 focus:ring-red-500"
                )}
              />
              {formState.crmIdentifier.error && (
                <p className="text-xs text-red-500 flex items-center gap-1 mt-1">
                  <AlertCircle size={12} />
                  {formState.crmIdentifier.error}
                </p>
              )}
            </div>
          </div>
 
          <div className="flex justify-end pt-4">
            <Button
              type="submit"
              disabled={isSubmitting || isComplete}
              className="min-w-[120px]"
            >
              {isSubmitting ? (
                <span className="flex items-center gap-1">
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Submitting...
                </span>
              ) : isComplete ? (
                <span className="flex items-center gap-1">
                  <Check size={16} />
                  Complete
                </span>
              ) : (
                "Register Patient"
              )}
            </Button>
          </div>
        </form>
      </Card>
 
      {/* Success message */}
      {isComplete && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <div className="flex flex-col items-center text-center">
              <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-4">
                <Check className="text-green-600" size={24} />
              </div>
              <h2 className="text-xl font-semibold mb-2">
                Registration Complete
              </h2>
              <p className="text-slate-500 mb-6">
                The new patient has been registered successfully.
              </p>
              <Button onClick={() => router.push("/patients")}>
                Return to Patients
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
 
 
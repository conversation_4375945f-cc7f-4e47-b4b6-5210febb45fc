// constants/sessionConfig.ts
export interface SessionConfig {
  // Session timeout in milliseconds (default: 15 minutes)
  timeoutDuration: number;
 
  // Warning time before logout in milliseconds (default: 2 minutes before timeout)
  warningDuration: number;
 
  // Events that should reset the idle timer
  activityEvents: string[];
 
  // Whether to show warning dialog before auto-logout
  showWarningDialog: boolean;
 
  // Whether session timeout is enabled
  enabled: boolean;
}
 
// Default configuration - controlled by developers only
export const DEFAULT_SESSION_CONFIG: SessionConfig = {
  // Session timeout duration in milliseconds (currently set to 1 minute)
  timeoutDuration: parseInt(process.env.NEXT_PUBLIC_SESSION_TIMEOUT_MINUTES || '15') * 60 * 1000,
 
  // Warning duration before timeout (should be shorter than timeout - currently 10 seconds)
  warningDuration: parseInt(process.env.NEXT_PUBLIC_SESSION_WARNING_MINUTES || '0.17') * 60 * 1000, // 0.17 minutes = ~10 seconds
 
  // Events that indicate user activity
  activityEvents: [
    'mousedown',
    'mousemove',
    'keypress',
    'scroll',
    'touchstart',
    'click',
    'focus',
    'blur'
  ],
 
  // Show warning dialog by default
  showWarningDialog: process.env.NEXT_PUBLIC_SHOW_SESSION_WARNING !== 'false',
 
  // Enable session timeout by default
  enabled: process.env.NEXT_PUBLIC_SESSION_TIMEOUT_ENABLED !== 'false'
};
 
// Helper function to get current session configuration
export const getSessionConfig = (): SessionConfig => {
  // Configuration is now controlled only by developers through environment variables or this file
  return DEFAULT_SESSION_CONFIG;
};
 
// Helper to convert milliseconds to human readable format
export const formatTimeRemaining = (milliseconds: number): string => {
  const minutes = Math.floor(milliseconds / 60000);
  const seconds = Math.floor((milliseconds % 60000) / 1000);
 
  if (minutes > 0) {
    return `${minutes}m ${seconds}s`;
  }
  return `${seconds}s`;
};
 
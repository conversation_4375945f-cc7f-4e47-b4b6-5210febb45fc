"use client";

import React, { useEffect, useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { AlertTriangle, LogOut, X } from "lucide-react";

interface SessionExpiredDialogProps {
  isOpen: boolean;
  onAutoLogout: () => void;
  onClose: () => void;
  warningDurationMs: number;
}

export function SessionExpiredDialog({
  isOpen,
  onAutoLogout,
  onClose,
  warningDurationMs,
}: SessionExpiredDialogProps) {
  const warningDurationSeconds = Math.ceil(warningDurationMs / 1000);
  const [countdown, setCountdown] = useState(warningDurationSeconds);

  // Auto logout countdown and cleanup
  useEffect(() => {
    if (isOpen) {
      setCountdown(warningDurationSeconds); // Reset countdown when dialog opens

      const interval = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(interval);
            // console.log("[SessionExpiredDialog] Countdown finished, triggering logout");
            onAutoLogout(); // Perform logout when countdown reaches 0
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => {
        clearInterval(interval);
      };
    } else {
      // Reset countdown when dialog closes
      setCountdown(warningDurationSeconds);
    }
  }, [isOpen, onAutoLogout, warningDurationSeconds]);

  const handleClose = () => {
    onClose();
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      handleClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              <DialogTitle>Session Expired</DialogTitle>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <DialogDescription className="space-y-4 text-center">
            <div className="flex justify-center">
              <LogOut className="h-12 w-12 text-red-500" />
            </div>
            <p className="text-base font-medium">
              Your session has expired due to inactivity.
            </p>
            <p className="text-sm text-muted-foreground">
              Please log in again to continue using the application.
            </p>
            <div className="text-sm font-medium text-red-600">
              {countdown > 0 ? `Auto-logout in: ${countdown} seconds` : "Logging out..."}
            </div>
            <p className="text-xs text-muted-foreground">
              {countdown > 0
                ? "You can close this dialog, but you will still be logged out automatically."
                : "Please wait while we log you out..."
              }
            </p>
          </DialogDescription>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
}

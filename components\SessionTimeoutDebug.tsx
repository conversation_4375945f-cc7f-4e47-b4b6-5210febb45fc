"use client";

import React from "react";
import { useIdleLogout } from "@/hooks/useIdleLogout";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Clock, Shield, RefreshCw } from "lucide-react";

/**
 * Debug component to show session timeout status
 * Only use this for testing - remove from production
 */
export function SessionTimeoutDebug() {
  const { isSessionExpired, logout, closeDialog } = useIdleLogout();

  // Show current localStorage values
  const [currentSettings, setCurrentSettings] = React.useState({
    minutes: '',
    enabled: '',
  });

  React.useEffect(() => {
    const updateSettings = () => {
      setCurrentSettings({
        minutes: localStorage.getItem('sessionTimeoutMinutes') || 'not set',
        enabled: localStorage.getItem('sessionTimeoutEnabled') || 'not set',
      });
    };

    updateSettings();
    const interval = setInterval(updateSettings, 1000);
    return () => clearInterval(interval);
  }, []);

  // Always show for debugging (remove this condition for production)
  // if (process.env.NODE_ENV === 'production') {
  //   return null;
  // }

  return (
    <Card className="fixed bottom-4 right-4 w-80 z-50 bg-white/95 backdrop-blur-sm border-2">
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2 text-sm">
          <Shield className="h-4 w-4" />
          Session Timeout Debug
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        <div className="text-xs space-y-1">
          <div><strong>Settings:</strong></div>
          <div>Minutes: {currentSettings.minutes}</div>
          <div>Enabled: {currentSettings.enabled}</div>
        </div>

        <div className="flex items-center gap-2 text-sm">
          <Clock className="h-4 w-4" />
          <span>
            {isSessionExpired
              ? "🔴 Session Expired!"
              : "🟢 Session Active"
            }
          </span>
        </div>

        <div className="flex gap-2">
          <Button
            size="sm"
            variant="destructive"
            onClick={logout}
            className="flex-1"
          >
            Manual Logout
          </Button>
          {isSessionExpired && (
            <Button
              size="sm"
              variant="outline"
              onClick={closeDialog}
              className="flex-1"
            >
              Close Dialog
            </Button>
          )}
        </div>

        <div className="text-xs space-y-1">
          <div className="font-medium">
            {isSessionExpired ? "🔴 Session Expired!" : "👁️ Monitoring activity..."}
          </div>
          <div>
            Timeout: {currentSettings.minutes} min
          </div>
          <div>
            Warning: 5 seconds before timeout
          </div>
          <div>
            Status: {isSessionExpired ? "EXPIRED" : "ACTIVE"}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

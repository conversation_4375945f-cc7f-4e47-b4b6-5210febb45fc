"use client";

import { useState } from "react";
import { LogOut } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/useAuth";

interface LogoutButtonProps {
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
  showIcon?: boolean;
  showText?: boolean;
}

export default function LogoutButton({ 
  variant = "ghost", 
  size = "default",
  className = "",
  showIcon = true,
  showText = true
}: LogoutButtonProps) {
  const { logout } = useAuth();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleLogout = async () => {
    setIsLoggingOut(true);
    try {
      // console.log("LogoutButton: Starting logout process");
      await logout();
    } catch (error) {
      console.error("LogoutButton logout error:", error);
      // Even if there's an error, try to force logout
      try {
        sessionStorage.clear();
        localStorage.clear();
        window.location.href = "/login";
      } catch (fallbackError) {
        console.error("LogoutButton fallback logout failed:", fallbackError);
      }
    } finally {
      setIsLoggingOut(false);
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleLogout}
      disabled={isLoggingOut}
      className={className}
    >
      {showIcon && <LogOut className={`h-4 w-4 ${showText ? 'mr-2' : ''}`} />}
      {showText && (isLoggingOut ? "Logging out..." : "Logout")}
    </Button>
  );
}

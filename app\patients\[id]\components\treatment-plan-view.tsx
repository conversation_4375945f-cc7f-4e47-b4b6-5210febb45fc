"use client";
 
import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Filter,
  Grid,
  BarChart3,
  List,
  Plus,
  Edit,
  Eye,
  Save,
  ChevronUp,
  ChevronDown,
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { API_BASE_URL } from "@/constants/apiRoutes";
import { useRouter } from "next/navigation";
import fetchWithRefresh from "@/constants/useRefreshAccessToken";
 
interface Procedure {
  id: number;
  patientVisitId: number;
  procedureCode: string;
  procedureName: string;
  toothNumber?: string;
  cost: string;
  duration: string;
  clinicNotes: string;
  severityType: "severe" | "moderate" | "mild" | string;
  urgency?: "immediate" | "short-term" | "long-term";
  status?: string;
  confidence?: number;
  clinicalRationale?: string;
  supportingXray?: string;
  supportingXrayUrl?: string;
  aiFindings?: string[];
  createdAt?: string;
  updatedAt?: string;
}
 
interface TreatmentPlanViewProps {
  patientId?: string;
  onOpenAnnotator?: (imageUrl: string, findingId?: string) => void;
}
 
export function TreatmentPlanView({
  patientId,
  onOpenAnnotator,
}: TreatmentPlanViewProps) {
  const { toast } = useToast();
  const router = useRouter();
  const [organizationMode, setOrganizationMode] = useState<
    "severity" | "quadrant" | "half-mouth" | "phase"
  >("severity");
  const [expandedIndex, setExpandedIndex] = useState<string | null>(null);
  const [procedures, setProcedures] = useState<Procedure[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingProcedure, setEditingProcedure] = useState<Procedure | null>(null);
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [newProcedure, setNewProcedure] = useState<Partial<Procedure>>({
    procedureCode: "",
    procedureName: "",
    toothNumber: "",
    severityType: "moderate",
    cost: "",
    duration: "",
    clinicNotes: "",
    status: "planned",
  });
 
  // useEffect(() => {
  //   const fetchProcedures = async () => {
  //     if (!patientId) return;
     
  //     try {
  //       setIsLoading(true);
  //       setError(null);
       
  //       // const response = await fetch(`${API_BASE_URL}/api/Treatment/visit/${patientId}`);
       
  //       // if (!response.ok) {
  //       //   throw new Error('Failed to fetch treatment data');
  //       // }
       
  //       // const { data } = await response.json();

  //            const response = await fetch(`${API_BASE_URL}/api/Treatment/visit/${patientId}`);
  //     const respJson = await response.json();

  //     if (!response.ok) {
  //       if (response.status === 404) {
  //         // “no records” → show empty list and bail
  //         setProcedures([]);
  //         return;
  //       }
  //       // other errors → throw
  //       throw new Error(respJson.data?.userMessage || 'Failed to fetch treatment data');
  //     }

  //     // 2xx: use the real data
  //     const { data } = respJson;
       
  //       const mappedProcedures = Array.isArray(data)
  //         ? data.map((proc: any) => ({
  //             ...proc,
  //             status: "planned",
  //             clinicalRationale: proc.clinicNotes || "Not provided",
  //             urgency: proc.severityType === "severe"
  //               ? "immediate"
  //               : proc.severityType === "moderate"
  //                 ? "short-term"
  //                 : "long-term",
  //             confidence: 90,
  //           }))
  //         : [];
       
  //       setProcedures(mappedProcedures);
  //     } catch (err) {
  //       setError(err instanceof Error ? err.message : 'Unknown error');
  //     } finally {
  //       setIsLoading(false);
  //     }
  //   };
 
  //   fetchProcedures();
  // }, [patientId]);
 
useEffect(() => {
    const fetchProcedures = async () => {
      if (!patientId) return;

      setIsLoading(true);
      setError(null);

      try {
        // ← use fetchWithRefresh instead of fetch
        const response = await fetchWithRefresh(
          `${API_BASE_URL}/api/Treatment/visit/${patientId}`,
          { method: "GET", credentials: "include" },
          router
        );

        // ← guard against null (refresh failed → redirected)
        if (!response) {
          throw new Error("Session expired, please log in again.");
        }

        const respJson = await response.json();

        if (!response.ok) {
          if (response.status === 404) {
            // no records: empty list
            setProcedures([]);
            return;
          }
          throw new Error(
            respJson.data?.userMessage || "Failed to fetch treatment data"
          );
        }

        // 2xx: transform data
        const data = respJson.data;
        const mapped = Array.isArray(data)
          ? data.map((proc: any) => ({
              ...proc,
              status: "planned",
              clinicalRationale: proc.clinicNotes || "Not provided",
              urgency:
                proc.severityType === "severe"
                  ? "immediate"
                  : proc.severityType === "moderate"
                  ? "short-term"
                  : "long-term",
              confidence: 90,
            }))
          : [];

        setProcedures(mapped);
      } catch (err: any) {
        setError(err.message || "Unknown error");
      } finally {
        setIsLoading(false);
      }
    };

    fetchProcedures();
  }, [patientId, router]);


  // const createNewProcedure = async () => {
  //   if (!patientId) return;
 
  //   try {
  //     setIsLoading(true);
     
  //     const payload = {
  //       patientVisitId: parseInt(patientId),
  //       procedureCode: newProcedure.procedureCode,
  //       procedureName: newProcedure.procedureName,
  //       toothNumber: newProcedure.toothNumber || "",
  //       cost: newProcedure.cost,
  //       duration: newProcedure.duration,
  //       clinicNotes: newProcedure.clinicNotes,
  //       severityType: newProcedure.severityType,
  //       createdBy: "dentist",
  //       updatedBy: "dentist"
  //     };
 
  //     const response = await fetch(`${API_BASE_URL}/api/Treatment`, {
  //       method: "POST",
  //       headers: {
  //         "Content-Type": "application/json",
  //       },
  //       body: JSON.stringify(payload),
  //     });
 
  //     if (!response.ok) {
  //       throw new Error('Failed to create new procedure');
  //     }
 
  //     const responseData = await response.json();
     
  //     const createdProcedure = responseData.data || responseData;
     
  //     setProcedures(prev => [...prev, {
  //       ...createdProcedure,
  //       status: "planned",
  //       clinicalRationale: createdProcedure.clinicNotes || newProcedure.clinicNotes || "Not provided",
  //       urgency: createdProcedure.severityType === "severe" ? "immediate" :
  //               createdProcedure.severityType === "moderate" ? "short-term" : "long-term",
  //       confidence: 90
  //     }]);
 
  //     toast({
  //       title: "Procedure added successfully",
  //       description: `${createdProcedure.procedureName || 'New procedure'} has been added to the treatment plan.`,
  //     });
 
  //     setIsAddingNew(false);
  //     setNewProcedure({
  //       procedureCode: "",
  //       procedureName: "",
  //       toothNumber: "",
  //       severityType: "moderate",
  //       cost: "",
  //       duration: "",
  //       clinicNotes: "",
  //       status: "planned",
  //     });
  //   } catch (err) {
  //     toast({
  //       title: "Error creating procedure",
  //       description: err instanceof Error ? err.message : "An unknown error occurred",
  //       variant: "destructive",
  //     });
  //   } finally {
  //     setIsLoading(false);
  //   }
  // };
 
const createNewProcedure = async () => {
    if (!patientId) return;

    setIsLoading(true);
    try {
      const payload = {
        patientVisitId: parseInt(patientId, 10),
        procedureCode: newProcedure.procedureCode,
        procedureName: newProcedure.procedureName,
        toothNumber: newProcedure.toothNumber || "",
        cost: newProcedure.cost,
        duration: newProcedure.duration,
        clinicNotes: newProcedure.clinicNotes,
        severityType: newProcedure.severityType,
        createdBy: "dentist",
        updatedBy: "dentist",
      };

      // ← use fetchWithRefresh
      const response = await fetchWithRefresh(
        `${API_BASE_URL}/api/Treatment`,
        {
          method: "POST",
          credentials: "include",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(payload),
        },
        router
      );

      // ← handle failed refresh
      if (!response) {
        throw new Error("Session expired, please log in again.");
      }
      if (!response.ok) {
        throw new Error("Failed to create new procedure");
      }

      const responseData = await response.json();
      const createdProcedure = responseData.data || responseData;

      setProcedures((prev) => [
        ...prev,
        {
          ...createdProcedure,
          status: "planned",
          clinicalRationale:
            createdProcedure.clinicNotes ||
            newProcedure.clinicNotes ||
            "Not provided",
          urgency:
            createdProcedure.severityType === "severe"
              ? "immediate"
              : createdProcedure.severityType === "moderate"
              ? "short-term"
              : "long-term",
          confidence: 90,
        },
      ]);

      toast({
        title: "Procedure added successfully",
        description: `${createdProcedure.procedureName ||
          "New procedure"} has been added to the treatment plan.`,
      });

      setIsAddingNew(false);
      setNewProcedure({
        procedureCode: "",
        procedureName: "",
        toothNumber: "",
        severityType: "moderate",
        cost: "",
        duration: "",
        clinicNotes: "",
        status: "planned",
      });
    } catch (err: any) {
      toast({
        title: "Error creating procedure",
        description: err.message || "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };


  // const updateProcedure = async () => {
  //   if (!editingProcedure || !patientId) return;
 
  //   try {
  //     setIsLoading(true);
 
  //     const payload = {
  //       id: editingProcedure.id,
  //       patientVisitId: editingProcedure.patientVisitId,
  //       procedureCode: editingProcedure.procedureCode,
  //       procedureName: editingProcedure.procedureName,
  //       toothNumber: editingProcedure.toothNumber || "",
  //       cost: editingProcedure.cost,
  //       duration: editingProcedure.duration,
  //       clinicNotes: editingProcedure.clinicNotes,
  //       severityType: editingProcedure.severityType,
  //       updatedBy: "dentist"
  //     };
 
  //     const response = await fetch(`${API_BASE_URL}/api/Treatment/${editingProcedure.id}`, {
  //       method: "PUT",
  //       headers: { "Content-Type": "application/json" },
  //       body: JSON.stringify(payload),
  //     });
 
  //     if (!response.ok) {
  //       throw new Error(`Failed to update procedure: ${response.statusText}`);
  //     }
 
  //     const resJson = await response.json();
     
  //     const updatedProcedure = {
  //       ...editingProcedure,
  //       ...resJson.data,
  //       clinicNotes: resJson.data.clinicNotes || editingProcedure.clinicNotes
  //     };
 
  //     setProcedures(prev =>
  //       prev.map(proc =>
  //         proc.id === updatedProcedure.id
  //           ? {
  //               ...proc,
  //               ...updatedProcedure,
  //               status: proc.status || "planned",
  //               urgency:
  //                 updatedProcedure.severityType === "severe"
  //                   ? "immediate"
  //                   : updatedProcedure.severityType === "moderate"
  //                   ? "short-term"
  //                   : "long-term",
  //               confidence: proc.confidence || 90,
  //               clinicalRationale: updatedProcedure.clinicNotes || proc.clinicalRationale
  //             }
  //           : proc
  //       )
  //     );
 
  //     toast({
  //       title: "Procedure updated",
  //       description: `${updatedProcedure.procedureName} has been updated.`,
  //     });
 
  //     setEditingProcedure(null);
  //     setExpandedIndex(null);
 
  //   } catch (err) {
  //     toast({
  //       title: "Update Failed",
  //       description: err instanceof Error ? err.message : "An unknown error occurred",
  //       variant: "destructive",
  //     });
  //   } finally {
  //     setIsLoading(false);
  //   }
  // };
 

const updateProcedure = async () => {
    if (!editingProcedure || !patientId) return;

    setIsLoading(true);
    try {
      const payload = {
        id: editingProcedure.id,
        patientVisitId: editingProcedure.patientVisitId,
        procedureCode: editingProcedure.procedureCode,
        procedureName: editingProcedure.procedureName,
        toothNumber: editingProcedure.toothNumber || "",
        cost: editingProcedure.cost,
        duration: editingProcedure.duration,
        clinicNotes: editingProcedure.clinicNotes,
        severityType: editingProcedure.severityType,
        updatedBy: "dentist",
      };

      // ← use fetchWithRefresh instead of fetch
      const response = await fetchWithRefresh(
        `${API_BASE_URL}/api/Treatment/${editingProcedure.id}`,
        {
          method: "PUT",
          credentials: "include",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(payload),
        },
        router
      );

      // ← guard against null (session expired → redirected)
      if (!response) {
        throw new Error("Session expired, please log in again.");
      }
      if (!response.ok) {
        throw new Error(`Failed to update procedure: ${response.statusText}`);
      }

      const resJson = await response.json();
      const updatedProcedure = {
        ...editingProcedure,
        ...resJson.data,
        clinicNotes: resJson.data.clinicNotes || editingProcedure.clinicNotes,
      };

      setProcedures((prev) =>
        prev.map((proc) =>
          proc.id === updatedProcedure.id
            ? {
                ...proc,
                ...updatedProcedure,
                status: proc.status || "planned",
                urgency:
                  updatedProcedure.severityType === "severe"
                    ? "immediate"
                    : updatedProcedure.severityType === "moderate"
                    ? "short-term"
                    : "long-term",
                confidence: proc.confidence || 90,
                clinicalRationale:
                  updatedProcedure.clinicNotes || proc.clinicalRationale,
              }
            : proc
        )
      );

      toast({
        title: "Procedure updated",
        description: `${updatedProcedure.procedureName} has been updated.`,
      });

      setEditingProcedure(null);
    } catch (err: any) {
      toast({
        title: "Update Failed",
        description: err.message || "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const deleteProcedure = async (id: number) => {
    try {
      setIsLoading(true);
     
      const response = await fetch(`${API_BASE_URL}/api/Treatment/${id}`, {
        method: "DELETE",
      });
 
      if (!response.ok) {
        throw new Error('Failed to delete procedure');
      }
 
      setProcedures(prev => prev.filter(proc => proc.id !== id));
 
      toast({
        title: "Procedure deleted successfully",
        description: "The procedure has been removed from the treatment plan.",
      });
    } catch (err) {
      toast({
        title: "Error deleting procedure",
        description: err instanceof Error ? err.message : "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };
 
  const organizedProcedures = () => {
    const grouped = procedures.reduce((acc, procedure) => {
      let key = "";
 
      switch (organizationMode) {
        case "severity":
          key = procedure.severityType;
          break;
        case "quadrant":
          if (procedure.toothNumber && procedure.toothNumber !== "All") {
            const toothNum = Number.parseInt(
              procedure.toothNumber.split(",")[0]
            );
            if (toothNum >= 1 && toothNum <= 8) key = "Upper Right";
            else if (toothNum >= 9 && toothNum <= 16) key = "Upper Left";
            else if (toothNum >= 17 && toothNum <= 24) key = "Lower Left";
            else if (toothNum >= 25 && toothNum <= 32) key = "Lower Right";
            else key = "General";
          } else {
            key = "General";
          }
          break;
        case "half-mouth":
          if (procedure.toothNumber && procedure.toothNumber !== "All") {
            const toothNum = Number.parseInt(
              procedure.toothNumber.split(",")[0]
            );
            key = toothNum <= 16 ? "Upper" : "Lower";
          } else {
            key = "General";
          }
          break;
        case "phase":
          key =
            procedure.urgency === "immediate"
              ? "Phase 1 - Immediate"
              : procedure.urgency === "short-term"
              ? "Phase 2 - Short-term"
              : "Phase 3 - Long-term";
          break;
      }
 
      if (!acc[key]) acc[key] = [];
      acc[key].push(procedure);
      return acc;
    }, {} as Record<string, Procedure[]>);
 
    const sortOrder =
      organizationMode === "severity"
        ? ["severe", "moderate", "mild"]
        : Object.keys(grouped).sort();
 
    return sortOrder
      .map((key) => ({
        title: key.charAt(0).toUpperCase() + key.slice(1),
        procedures: grouped[key] || [],
      }))
      .filter((group) => group.procedures.length > 0);
  };
 
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "severe":
        return "bg-red-100 text-red-800 border-red-200";
      case "moderate":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "mild":
        return "bg-green-100 text-green-800 border-green-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };
 
  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case "immediate":
        return "bg-red-100 text-red-800 border-red-200";
      case "short-term":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "long-term":
        return "bg-green-100 text-green-800 border-green-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };
 
  const handleEditProcedure = (procedure: Procedure) => {
    setEditingProcedure({ ...procedure });
  };
 
  const handleViewXray = (procedure: Procedure) => {
    if (procedure.supportingXrayUrl && onOpenAnnotator) {
      const findingId = procedure.aiFindings?.[0];
      onOpenAnnotator(procedure.supportingXrayUrl, findingId);
    }
  };
 
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <p>Loading treatment data...</p>
      </div>
    );
  }
 
  if (error) {
    return (
      <div className="flex justify-center items-center h-64">
        <p className="text-red-500">Error: {error}</p>
      </div>
    );
  }
 
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-semibold text-gray-900">
          Treatment Organized by{" "}
          {organizationMode.charAt(0).toUpperCase() + organizationMode.slice(1)}
        </h2>
 
        <div className="flex items-center gap-3">
          {/* Add New Procedure */}
          <Dialog open={isAddingNew} onOpenChange={setIsAddingNew}>
            <DialogTrigger asChild>
              <Button className="bg-green-600 hover:bg-green-700 text-white">
                <Plus className="w-4 h-4 mr-2" />
                Add Procedure
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Add New Treatment Procedure</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">
                      Procedure Code
                    </label>
                    <Input
                      value={newProcedure.procedureCode}
                      onChange={(e) =>
                        setNewProcedure({
                          ...newProcedure,
                          procedureCode: e.target.value,
                        })
                      }
                      placeholder="e.g., D2392"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">
                      Procedure Name
                    </label>
                    <Input
                      value={newProcedure.procedureName}
                      onChange={(e) =>
                        setNewProcedure({
                          ...newProcedure,
                          procedureName: e.target.value,
                        })
                      }
                      placeholder="e.g., Composite Restoration"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <label className="text-sm font-medium">Tooth Number</label>
                    <Input
                      value={newProcedure.toothNumber}
                      onChange={(e) =>
                        setNewProcedure({
                          ...newProcedure,
                          toothNumber: e.target.value,
                        })
                      }
                      placeholder="e.g., 14"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Cost</label>
                    <Input
                      value={newProcedure.cost}
                      onChange={(e) =>
                        setNewProcedure({
                          ...newProcedure,
                          cost: e.target.value,
                        })
                      }
                      placeholder="e.g., $200-350"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Duration</label>
                    <Input
                      value={newProcedure.duration}
                      onChange={(e) =>
                        setNewProcedure({
                          ...newProcedure,
                          duration: e.target.value,
                        })
                      }
                      placeholder="e.g., 60 min"
                    />
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium">Severity</label>
                  <select
                    value={newProcedure.severityType}
                    onChange={(e) =>
                      setNewProcedure({
                        ...newProcedure,
                        severityType: e.target.value as "severe" | "moderate" | "mild",
                      })
                    }
                    className="w-full border rounded-md p-2"
                  >
                    <option value="severe">Severe</option>
                    <option value="moderate">Moderate</option>
                    <option value="mild">Mild</option>
                  </select>
                </div>
                <div>
                  <label className="text-sm font-medium">
                    Clinical Notes
                  </label>
                  <Textarea
                    value={newProcedure.clinicNotes}
                    onChange={(e) =>
                      setNewProcedure({
                        ...newProcedure,
                        clinicNotes: e.target.value,
                      })
                    }
                    placeholder="Describe the clinical reasoning for this procedure..."
                    className="min-h-[100px]"
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setIsAddingNew(false)}
                  >
                    Cancel
                  </Button>
                  <Button onClick={createNewProcedure} disabled={isLoading}>
                    {isLoading ? "Adding..." : "Add Procedure"}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
 
          {/* Organization Controls */}
          <div className="flex items-center gap-2 bg-white border border-gray-200 rounded-lg p-1">
            <Button
              variant={organizationMode === "severity" ? "default" : "ghost"}
              size="sm"
              onClick={() => setOrganizationMode("severity")}
              className={
                organizationMode === "severity"
                  ? "bg-gray-900 text-white"
                  : "text-gray-700 hover:bg-gray-100"
              }
            >
              <Filter className="w-4 h-4" />
            </Button>
            <Button
              variant={organizationMode === "quadrant" ? "default" : "ghost"}
              size="sm"
              onClick={() => setOrganizationMode("quadrant")}
              className={
                organizationMode === "quadrant"
                  ? "bg-gray-900 text-white"
                  : "text-gray-700 hover:bg-gray-100"
              }
            >
              <Grid className="w-4 h-4" />
            </Button>
            <Button
              variant={organizationMode === "half-mouth" ? "default" : "ghost"}
              size="sm"
              onClick={() => setOrganizationMode("half-mouth")}
              className={
                organizationMode === "half-mouth"
                  ? "bg-gray-900 text-white"
                  : "text-gray-700 hover:bg-gray-100"
              }
            >
              <BarChart3 className="w-4 h-4" />
            </Button>
            <Button
              variant={organizationMode === "phase" ? "default" : "ghost"}
              size="sm"
              onClick={() => setOrganizationMode("phase")}
              className={
                organizationMode === "phase"
                  ? "bg-gray-900 text-white"
                  : "text-gray-700 hover:bg-gray-100"
              }
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
 
      {/* Treatment Groups */}
      {procedures.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-12 border border-dashed border-gray-300 rounded-lg bg-gray-50">
          {/* <div className="text-center space-y-4">
            <h3 className="text-lg font-medium text-gray-900">No procedures found</h3>
         
            <Dialog open={isAddingNew} onOpenChange={setIsAddingNew}>
              <DialogTrigger asChild>
                <Button className="bg-green-600 hover:bg-green-700 text-white">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Procedure
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Add New Treatment Procedure</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                
                </div>
              </DialogContent>
            </Dialog>
          </div> */}
            <p className="text-gray-500">Add your first procedure to create a treatment plan</p>
        </div>
      ) : (
        <div className="space-y-8">
          {organizedProcedures().map((group) => (
            <div key={group.title} className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-3">
                  {group.title}
                  <Badge
                    variant="outline"
                    className="bg-gray-100 text-gray-700 border-gray-300"
                  >
                    {group.procedures.length} procedure
                    {group.procedures.length !== 1 ? "s" : ""}
                  </Badge>
                </h3>
              </div>
 
              <div className="space-y-4">
                {group.procedures.map((procedure, index) => (
                  <Card
                    key={index}
                    className="bg-white border-gray-200 hover:shadow-lg transition-shadow"
                  >
                    <CardHeader className="pb-4">
                      <div className="flex items-start justify-between">
                        <div className="flex flex-wrap items-center gap-4">
                          <Badge
                            variant="outline"
                            className="bg-gray-100 text-gray-700 border-gray-300 font-mono"
                          >
                            {procedure.procedureCode}
                          </Badge>
                          <h4 className="text-lg font-semibold text-gray-900">
                            {procedure.procedureName}
                          </h4>
                          {procedure.confidence && (
                            <Badge className="bg-blue-100 text-blue-700 border-blue-200">
                              {procedure.confidence}% AI Confidence
                            </Badge>
                          )}
                          {procedure.toothNumber && (
                            <span className="font-medium text-gray-900">
                              Teeth: {procedure.toothNumber}
                            </span>
                          )}
                          <span className="text-gray-600">
                            {procedure.duration}
                          </span>
                          <span className="font-medium text-gray-900">
                            {procedure.cost}
                          </span>
                          {procedure.urgency && (
                            <Badge
                              variant="outline"
                              className={getUrgencyColor(procedure.urgency)}
                            >
                              {procedure.urgency}
                            </Badge>
                          )}
                          <Badge
                            variant="outline"
                            className={getSeverityColor(procedure.severityType)}
                          >
                            {procedure.severityType}
                          </Badge>
                        </div>
 
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              setExpandedIndex(
                                expandedIndex === procedure.procedureCode
                                  ? null
                                  : procedure.procedureCode
                              )
                            }
                            className="border-gray-300 text-gray-700 hover:bg-gray-50"
                          >
                            {expandedIndex === procedure.procedureCode ? (
                              <ChevronUp className="h-4 w-4" />
                            ) : (
                              <ChevronDown className="h-4 w-4" />
                            )}
                          </Button>
                          {expandedIndex === procedure.procedureCode && (
                            <Dialog>
                              <DialogTrigger asChild>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="border-gray-300 text-gray-700 hover:bg-gray-50"
                                  onClick={() => handleEditProcedure(procedure)}
                                >
                                  <Edit className="w-4 h-4" />
                                </Button>
                              </DialogTrigger>
                              <DialogContent className="max-w-2xl">
                                <DialogHeader>
                                  <DialogTitle>
                                    Edit Treatment Procedure
                                  </DialogTitle>
                                </DialogHeader>
                                {editingProcedure && (
                                <div className="space-y-4">
                                  <div className="grid grid-cols-2 gap-4">
                                    <div>
                                      <label className="text-sm font-medium">
                                        Procedure Code
                                      </label>
                                      <Input
                                        value={editingProcedure.procedureCode}
                                        onChange={(e) =>
                                          setEditingProcedure({
                                            ...editingProcedure,
                                            procedureCode: e.target.value,
                                          })
                                        }
                                      />
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium">
                                        Procedure Name
                                      </label>
                                      <Input
                                        value={editingProcedure.procedureName}
                                        onChange={(e) =>
                                          setEditingProcedure({
                                            ...editingProcedure,
                                            procedureName: e.target.value,
                                          })
                                        }
                                      />
                                    </div>
                                  </div>
                                  <div className="grid grid-cols-3 gap-4">
                                    <div>
                                      <label className="text-sm font-medium">
                                        Tooth Number
                                      </label>
                                      <Input
                                        value={editingProcedure.toothNumber}
                                        onChange={(e) =>
                                          setEditingProcedure({
                                            ...editingProcedure,
                                            toothNumber: e.target.value,
                                          })
                                        }
                                      />
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium">
                                        Cost
                                      </label>
                                      <Input
                                        value={editingProcedure.cost}
                                        onChange={(e) =>
                                          setEditingProcedure({
                                            ...editingProcedure,
                                            cost: e.target.value,
                                          })
                                        }
                                      />
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium">
                                        Duration
                                      </label>
                                      <Input
                                        value={editingProcedure.duration}
                                        onChange={(e) =>
                                          setEditingProcedure({
                                            ...editingProcedure,
                                            duration: e.target.value,
                                          })
                                        }
                                      />
                                    </div>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">
                                      Severity
                                    </label>
                                    <select
                                      value={editingProcedure.severityType}
                                      onChange={(e) =>
                                        setEditingProcedure({
                                          ...editingProcedure,
                                          severityType: e.target.value as "severe" | "moderate" | "mild",
                                        })
                                      }
                                      className="w-full border rounded-md p-2"
                                    >
                                      <option value="severe">Severe</option>
                                      <option value="moderate">Moderate</option>
                                      <option value="mild">Mild</option>
                                    </select>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">
                                      Clinical Notes
                                    </label>
                                    <Textarea
                                      value={editingProcedure.clinicNotes}
                                      onChange={(e) =>
                                        setEditingProcedure({
                                          ...editingProcedure,
                                          clinicNotes: e.target.value,
                                        })
                                      }
                                      className="min-h-[100px]"
                                    />
                                  </div>
                                  <div className="flex justify-end gap-2">
                                    <Button
                                      variant="outline"
                                      onClick={() => setEditingProcedure(null)}
                                    >
                                      Cancel
                                    </Button>
                                   <Button
  onClick={() => {
    // console.log("Save button clicked");
    updateProcedure();
  }}
  disabled={isLoading}
>
                                      <Save className="w-4 h-4 mr-2" />
                                      {isLoading ? "Saving..." : "Save Changes"}
                                    </Button>
                                  </div>
                                </div>
                              )}
                              </DialogContent>
                            </Dialog>
                          )}
                        </div>
                      </div>
                    </CardHeader>
 
                    {expandedIndex === procedure.procedureCode && (
                      <CardContent className="space-y-4">
                        <div>
                          <h5 className="font-medium text-gray-900 mb-2">
                            Clinical Rationale:
                          </h5>
                          <p className="text-gray-700 leading-relaxed">
                            {procedure.clinicNotes}
                          </p>
                        </div>
 
                        {procedure.supportingXray && (
                          <div>
                            <h5 className="font-medium text-gray-900 mb-2">
                              Supporting X-Ray:
                            </h5>
                            <div className="flex items-center gap-4">
                              <div className="w-16 h-16 bg-gray-800 rounded border border-gray-300"></div>
                              <div className="flex-1">
                                <p className="font-medium text-gray-900">
                                  {procedure.supportingXray}
                                </p>
                                <p className="text-sm text-gray-600">
                                  Click to view full size X-ray with annotations
                                </p>
                              </div>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleViewXray(procedure)}
                                className="border-blue-300 text-blue-600 hover:bg-blue-50"
                              >
                                <Eye className="w-4 h-4 mr-2" />
                                View X-Ray
                              </Button>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    )}
                  </Card>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
 
export default TreatmentPlanView;
 